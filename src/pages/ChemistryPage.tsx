import { useState, use<PERSON>em<PERSON>, useEffect } from "react"; // Added useEffect
import { useTranslation } from "react-i18next";
import { supabase } from "@/lib/supabase";
import UnifiedLayout from "@/components/layout/UnifiedLayout";
import { Search, Handshake, ArrowRight, Users, UsersRound, BarChartHorizontal } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ianGrid,
    <PERSON>lt<PERSON>,
    ResponsiveContainer,
    Label as RechartsLabel,
    ZAxis
} from "recharts";
import { parseISO } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { useMediaQuery } from "@/hooks/use-media-query";

// localStorage Keys
const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
const MATCHES_STORAGE_KEY = 'soccerMatchesData';

// --- Interfaces ---
interface Player { id: number; name: string; skills?: number; effort?: number; stamina?: number; group_id?: string; }
interface Goalscorer { team: 'A' | 'B'; playerId: number; }
interface Match { id: number; date: Date; teama: number[]; teamb: number[]; scoreA: number | null; scoreB: number | null; winner?: 'A' | 'B' | 'Draw'; goalscorers?: Goalscorer[]; youtubeLink?: string; group_id?: string; }
interface ChemistryPair { player1: number; player2: number; played: number; wins: number; winRate: number; }
interface GroupChemistry { players: number[]; played: number; wins: number; winRate: number; }

// Function to load data from localStorage
function loadData<T>(key: string, fallback: T[]): T[] {
    try {
      const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
      if (storedData) {
          const parsedData = JSON.parse(storedData) as any[];
          if (key === MATCHES_STORAGE_KEY) {
              return parsedData.map(item => ({
                  ...item,
                  date: item.date ? parseISO(item.date) : new Date(0)
              })) as T[];
          }
          return parsedData as T[];
      }
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
    }
    return fallback;
}

// Load players and matches data
const playersData = loadData<Player>(PLAYERS_STORAGE_KEY, []);
const matchesData = loadData<Match>(MATCHES_STORAGE_KEY, []);

// Placeholder for Duo Chemistry - Recommend calculating this from matchesData
const duoChemistryData: ChemistryPair[] = [
  // Placeholder - calculate this from matchesData if possible
];

// --- Helper Functions ---

function getCombinations<T>(array: T[], k: number): T[][] {
  const result: T[][] = [];
  function combine(start: number, currentCombination: T[]) {
    if (currentCombination.length === k) {
      result.push([...currentCombination]);
      return;
    }
    for (let i = start; i < array.length; i++) {
      currentCombination.push(array[i]);
      combine(i + 1, currentCombination);
      currentCombination.pop();
    }
  }
  combine(0, []);
  return result;
}

interface ScatterPoint { x: number; y: number; z: string; }

const CustomTooltip = ({ active, payload, label }: any) => {
    const { t } = useTranslation();
    if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
            <div className="bg-background border rounded-md p-2 shadow-lg text-sm">
                <p className="font-semibold mb-1">{data.z}</p>
                <p>{t('chemistry.gamesPlayed', 'Games Played')}: {data.x}</p>
                <p>{t('chemistry.winRate', 'Win Rate')}: {data.y.toFixed(1)}%</p>
            </div>
        );
    }
    return null;
};

// --- Component ---

const ChemistryPage = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [duoData, setDuoData] = useState<ChemistryPair[]>([]);

  const [search, setSearch] = useState("");
  const [selectedPlayer, setSelectedPlayer] = useState<string>("");
  const [minGames, setMinGames] = useState(10); // Changed default to 10 games
  const [viewMode, setViewMode] = useState<"duos" | "trios" | "quads">("duos");
  const [currentPage, setCurrentPage] = useState(1);
  const isMobile = useMediaQuery("(max-width: 768px)");
  const itemsPerPage = isMobile ? 6 : 20; // Changed from Infinity to 20 for desktop

  // Calculate maximum games played for slider
  const maxGamesPlayed = useMemo(() => {
    if (matches.length === 0) return 20;
    const playerGameCounts = new Map<number, number>();

    matches.forEach(match => {
      [...match.teamA, ...match.teamB].forEach(playerId => {
        playerGameCounts.set(playerId, (playerGameCounts.get(playerId) || 0) + 1);
      });
    });

    return Math.max(...Array.from(playerGameCounts.values()), 20);
  }, [matches]);

  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  const { trios, quads } = useMemo(() => {
    const trioStats: { [key: string]: { played: number; wins: number } } = {};
    const quadStats: { [key: string]: { played: number; wins: number } } = {};
    matches.forEach(match => {
      const processTeam = (team: number[], isWinner: boolean) => {
        if (!team || team.length < 3) return;

        const teamTrios = getCombinations(team, 3);
        teamTrios.forEach(trio => {
          const key = [...trio].sort((a, b) => a - b).join('-');
          if (!trioStats[key]) trioStats[key] = { played: 0, wins: 0 };
          trioStats[key].played++;
          if (isWinner) trioStats[key].wins++;
        });

        if (team.length >= 4) {
           const teamQuads = getCombinations(team, 4);
           teamQuads.forEach(quad => {
             const key = [...quad].sort((a, b) => a - b).join('-');
             if (!quadStats[key]) quadStats[key] = { played: 0, wins: 0 };
             quadStats[key].played++;
             if (isWinner) quadStats[key].wins++;
           });
        }
      };

      // Use teama/teamb directly
      processTeam(match.teama || [], match.winner === 'A');
      processTeam(match.teamb || [], match.winner === 'B');
    });
    const calculateResults = (stats: { [key: string]: { played: number; wins: number } }): GroupChemistry[] => {
        return Object.entries(stats).map(([key, data]) => {
            const playerIds = key.split('-').map(Number);
            const winRate = data.played > 0 ? (data.wins / data.played) * 100 : 0;
            return { players: playerIds, played: data.played, wins: data.wins, winRate };
        });
    }
    return { trios: calculateResults(trioStats), quads: calculateResults(quadStats) };
  }, [matches]);

  const filterAndSort = (data: any[], mode: "duos" | "trios" | "quads") => {
    const selectedPlayerId = selectedPlayer ? parseInt(selectedPlayer) : null;
    const searchTermLower = search.toLowerCase();

    return data
      .filter((item) => {
          let playerIds: number[] = [];
          if (!item) return false;
          if (mode === 'duos') {
             if (!item.player1 || !item.player2) return false;
             playerIds = [item.player1, item.player2];
          } else {
             if (!item.players || !Array.isArray(item.players)) return false;
             playerIds = item.players;
          }

          if (selectedPlayerId && !playerIds.includes(selectedPlayerId)) return false;
          if (typeof item.played !== 'number' || item.played < minGames) return false;
          if (search && !playerIds.some(id => getPlayerName(id)?.toLowerCase().includes(searchTermLower))) return false;
          return true;
      })
      // Always sort by win rate in descending order
      .sort((a, b) => {
        // First sort by win rate
        const winRateCompare = (b.winRate ?? 0) - (a.winRate ?? 0);
        if (winRateCompare !== 0) return winRateCompare;

        // If win rates are equal, sort by number of games played
        const gamesCompare = (b.played ?? 0) - (a.played ?? 0);
        if (gamesCompare !== 0) return gamesCompare;

        // If games played are equal, sort alphabetically by player names
        const aNames = mode === 'duos'
          ? `${getPlayerName(a.player1)}-${getPlayerName(a.player2)}`
          : a.players.map(getPlayerName).join('-');
        const bNames = mode === 'duos'
          ? `${getPlayerName(b.player1)}-${getPlayerName(b.player2)}`
          : b.players.map(getPlayerName).join('-');
        return aNames.localeCompare(bNames);
      });
  }

  // Debugging logs for filtered data
  const filteredDuos = useMemo(() => {
    const result = filterAndSort(duoData, "duos");
    console.log("Filtered Duos:", result);
    return result;
  }, [duoData, search, selectedPlayer, minGames, players]);

  const filteredTrios = useMemo(() => {
    const result = filterAndSort(trios, "trios");
    console.log("Filtered Trios:", result);
    return result;
  }, [trios, search, selectedPlayer, minGames, players]);

  const filteredQuads = useMemo(() => {
    const result = filterAndSort(quads, "quads");
    console.log("Filtered Quads:", result);
    return result;
  }, [quads, search, selectedPlayer, minGames, players]);

  // Move useEffect before paginatedData
  useEffect(() => {
    // Reset currentPage when view mode changes
    setCurrentPage(1);
  }, [viewMode]);

  // Update paginatedData logic
  const paginatedData = useMemo(() => {
    let sourceData: any[] = [];
    if (viewMode === "duos") sourceData = filteredDuos;
    else if (viewMode === "trios") sourceData = filteredTrios;
    else sourceData = filteredQuads;

    console.log("Source Data for Pagination:", sourceData);

    // Handle empty data case
    if (!sourceData || sourceData.length === 0) {
      return { data: [], totalPages: 1, totalItems: 0 };
    }

    const totalItems = sourceData.length;
    const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));

    // Ensure current page is valid
    const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages);

    // Calculate slice indices
    const start = (validCurrentPage - 1) * itemsPerPage;
    const end = start + itemsPerPage;

    // Get paginated data
    const paginated = sourceData.slice(start, end);
    console.log("Paginated Data:", paginated);

    return {
      data: paginated,
      totalPages,
      totalItems
    };
  }, [filteredDuos, filteredTrios, filteredQuads, viewMode, currentPage, itemsPerPage]);

  useEffect(() => {
    // Reset currentPage when viewMode or filters change
    setCurrentPage(1);
  }, [viewMode, search, selectedPlayer, minGames]);

  const scatterData = useMemo((): ScatterPoint[] => {
    let sourceData: any[] = [];
    if (viewMode === 'duos') sourceData = filteredDuos;
    else if (viewMode === 'trios') sourceData = filteredTrios;
    else sourceData = filteredQuads;

    return sourceData.map(item => {
      // Default values to prevent NaN
      const defaultPoint = { x: 0, y: 0, z: 'Unknown' };

      // Return default if item is invalid
      if (!item) return defaultPoint;

      // Get name string
      let names = "";
      try {
        if (viewMode === 'duos' && item.player1 && item.player2) {
          names = `${getPlayerName(item.player1)} & ${getPlayerName(item.player2)}`;
        } else if (Array.isArray(item.players)) {
          names = item.players.map(getPlayerName).join(', ');
        }
      } catch (e) {
        console.error('Error getting player names:', e);
      }

      // Ensure numbers are valid
      const played = parseInt(String(item.played), 10);
      const winRate = parseFloat(String(item.winRate));

      // Return point with validated values
      return {
        x: !isNaN(played) ? played : 0,
        y: !isNaN(winRate) ? winRate : 0,
        z: names || 'Unknown Group'
      };
    }).filter(point => point.x > 0 && point.y >= 0 && point.y <= 100);  // Only keep valid points
  }, [viewMode, filteredDuos, filteredTrios, filteredQuads, players, getPlayerName]);

  const getWinRateBadgeClass = (rate: number | null | undefined) => {
    const numRate = rate ?? 0;
    if (numRate >= 70) return "bg-green-100 text-green-800 border-green-300";
    if (numRate >= 50) return "bg-yellow-100 text-yellow-800 border-yellow-300";
    return "bg-red-100 text-red-800 border-red-300";
  }

  const renderPagination = () => {
    if (itemsPerPage === Infinity || paginatedData.totalPages <= 1) return null;

    const validCurrentPage = Math.min(currentPage, paginatedData.totalPages);
    const start = ((validCurrentPage - 1) * itemsPerPage) + 1;
    const end = Math.min(validCurrentPage * itemsPerPage, paginatedData.totalItems);

    return (
      <div className="flex items-center justify-between mt-4 px-2">
        <div className="text-sm text-muted-foreground">
          {t('common.showing', 'Showing')} {start} {t('common.to', 'to')} {end} {t('common.of', 'of')} {paginatedData.totalItems}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
            disabled={validCurrentPage === 1}
          >
            {t('common.previous', 'Previous')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(p => Math.min(paginatedData.totalPages, p + 1))}
            disabled={validCurrentPage === paginatedData.totalPages}
          >
            {t('common.next', 'Next')}
          </Button>
        </div>
      </div>
    );
  };

  // Move useMediaQuery hook to component level
  const renderTableContent = () => {
    const dataToRender = paginatedData.data;
    let headers: React.ReactNode[] = [];
    let renderRow: (item: any) => React.ReactNode;

    // Debugging logs to verify data
    console.log('View Mode:', viewMode);
    console.log('Data to render:', dataToRender);

    if (isMobile) {
      // Mobile view rendering
      return (
        <>
          <div className="rounded-md border divide-y">
            {dataToRender.map((item) => (
              <div key={item.id || Math.random()} className="p-4 flex flex-col gap-2">
                {viewMode === 'duos' ? (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.player', 'Players')}</span>
                      <div className="font-medium">
                        {getPlayerName(item.player1)} → {getPlayerName(item.player2)}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.played', 'Games')}</span>
                      <div className="font-medium">{item.played}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.winRate', 'Win Rate')}</span>
                      <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                        {(item.winRate ?? 0).toFixed(1)}%
                      </Badge>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.player', 'Players')}</span>
                      <div className="font-medium">
                        {(item.players || []).map(getPlayerName).join(', ')}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.played', 'Games')}</span>
                      <div className="font-medium">{item.played}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">{t('chemistry.winRate', 'Win Rate')}</span>
                      <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                        {(item.winRate ?? 0).toFixed(1)}%
                      </Badge>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
          {renderPagination()}
        </>
      );
    }

    // Desktop view rendering
    switch (viewMode) {
      case 'trios':
        headers = [
          <TableHead key="trio-players">{t('chemistry.trios')}</TableHead>,
          <TableHead key="trio-games" className="text-center">{t('chemistry.played')}</TableHead>,
          <TableHead key="trio-wins" className="text-center">{t('players.won')}</TableHead>,
          <TableHead key="trio-rate" className="text-right">{t('chemistry.winRate')}</TableHead>,
        ];
        renderRow = (item) => (
          <TableRow key={`trio-${item.players?.join('-')}`}>
            <TableCell className="font-medium">{(item.players || []).map(getPlayerName).join(', ')}</TableCell>
            <TableCell className="text-center">{item.played}</TableCell>
            <TableCell className="text-center">{item.wins}</TableCell>
            <TableCell className="text-right">
              <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                {(item.winRate ?? 0).toFixed(1)}%
              </Badge>
            </TableCell>
          </TableRow>
        );
        break;

      case 'quads':
        headers = [
          <TableHead key="quad-players">{t('chemistry.quads')}</TableHead>,
          <TableHead key="quad-games" className="text-center">{t('chemistry.played')}</TableHead>,
          <TableHead key="quad-wins" className="text-center">{t('players.won')}</TableHead>,
          <TableHead key="quad-rate" className="text-right">{t('chemistry.winRate')}</TableHead>,
        ];
        renderRow = (item) => (
          <TableRow key={`quad-${item.players?.join('-')}`}>
            <TableCell className="font-medium">{(item.players || []).map(getPlayerName).join(', ')}</TableCell>
            <TableCell className="text-center">{item.played}</TableCell>
            <TableCell className="text-center">{item.wins}</TableCell>
            <TableCell className="text-right">
              <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                {(item.winRate ?? 0).toFixed(1)}%
              </Badge>
            </TableCell>
          </TableRow>
        );
        break;

      default: // Duos
        headers = [
          <TableHead key="duo-p1">{t('chemistry.player')} 1</TableHead>,
          <TableHead key="duo-arrow" className="w-[50px] text-center"></TableHead>,
          <TableHead key="duo-p2">{t('chemistry.player')} 2</TableHead>,
          <TableHead key="duo-games" className="text-center">{t('chemistry.played')}</TableHead>,
          <TableHead key="duo-wins" className="text-center">{t('players.won')}</TableHead>,
          <TableHead key="duo-rate" className="text-right">{t('chemistry.winRate')}</TableHead>,
        ];
        renderRow = (item) => {
          // Debugging logs for each row
          console.log('Rendering duo row:', item);

          return (
            <TableRow key={`duo-${item.player1}-${item.player2}`}>
              <TableCell className="font-medium">{getPlayerName(item.player1)}</TableCell>
              <TableCell className="text-center"><ArrowRight className="inline-block h-4 w-4" /></TableCell>
              <TableCell>{getPlayerName(item.player2)}</TableCell>
              <TableCell className="text-center">{item.played}</TableCell>
              <TableCell className="text-center">{item.wins}</TableCell>
              <TableCell className="text-right">
                <Badge variant="outline" className={getWinRateBadgeClass(item.winRate)}>
                  {(item.winRate ?? 0).toFixed(1)}%
                </Badge>
              </TableCell>
            </TableRow>
          );
        };
    }

    return (
      <>
        <div className="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>{headers}</TableRow>
            </TableHeader>
            <TableBody>
              {!dataToRender || dataToRender.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={headers.length} className="h-24 text-center">
                    {t('chemistry.noChemistryData', 'No chemistry data available')} ({viewMode})
                  </TableCell>
                </TableRow>
              ) : (
                dataToRender.map((item) => renderRow(item))
              )}
            </TableBody>
          </Table>
        </div>
        {renderPagination()}
      </>
    );
  };

  const calculateDuoChemistry = (matches: Match[]) => {
    const duoStats = new Map<string, { played: number; wins: number }>();

    matches.forEach(match => {
      // Use teama/teamb directly from Supabase data
      const teamA = match.teama || [];
      const teamB = match.teamb || [];

      // Process team A duos
      const processTeam = (team: number[], isWinningTeam: boolean) => {
        if (team.length < 2) return;

        const duos = getCombinations(team, 2);
        duos.forEach(([p1, p2]) => {
          const key = [p1, p2].sort((a, b) => a - b).join('-');
          const stats = duoStats.get(key) || { played: 0, wins: 0 };
          stats.played++;
          if (isWinningTeam) stats.wins++;
          duoStats.set(key, stats);
        });
      };

      processTeam(teamA, match.winner === 'A');
      processTeam(teamB, match.winner === 'B');
    });

    return Array.from(duoStats.entries())
      .map(([key, stats]) => {
        const [p1, p2] = key.split('-').map(Number);
        return {
          player1: p1,
          player2: p2,
          played: stats.played,
          wins: stats.wins,
          winRate: (stats.wins / stats.played) * 100
        };
      })
      .filter(duo => !isNaN(duo.player1) && !isNaN(duo.player2) && duo.played > 0)
      .sort((a, b) => b.winRate - a.winRate);
  };

  const selectedGroupId = localStorage.getItem('selectedGroupId');

  // Update useEffect to properly transform the data
  useEffect(() => {
    const fetchData = async () => {
      if (!selectedGroupId) {
        console.warn("No group selected. Please select a group first.");
        setMatches([]);
        setPlayers([]);
        setDuoData([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        const [{ data: matchesData }, { data: playersData }] = await Promise.all([
          supabase.from('matches').select('*').eq('group_id', selectedGroupId),
          supabase.from('players').select('*').eq('group_id', selectedGroupId)
        ]);

        if (!matchesData || !playersData) throw new Error('Failed to fetch data');

        // Keep original properties from Supabase
        const transformedMatches = matchesData;

        setMatches(transformedMatches);
        setPlayers(playersData);

        // Calculate duo chemistry with original properties
        const duoChemistry = calculateDuoChemistry(transformedMatches);
        setDuoData(duoChemistry);

      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: t('chemistry.loadError', 'Error loading data'),
          description: error instanceof Error ? error.message : String(error),
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [toast, selectedGroupId]);


  if (loading) {
    return (
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">{t('chemistry.title', 'Player Chemistry')}</h1>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-soccer-primary" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h1 className="text-2xl font-bold">{t('chemistry.title', 'Player Chemistry')}</h1>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Card 1: Analysis Table */}
        <Card className="lg:col-span-1" noScale>
          <CardHeader className="pb-3">
            <CardTitle>{t('chemistry.analysisTitle', 'Chemistry Analysis')}</CardTitle>
            <CardDescription>
              {t('chemistry.analysisDescription', 'View how player groups perform when placed on the same team.')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="relative flex-grow">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input placeholder={t('chemistry.searchPlayers')} value={search} onChange={(e) => setSearch(e.target.value)} className="pl-8" />
                </div>
                <div className="flex gap-2 items-center">
                    <span className="text-sm whitespace-nowrap">{t('chemistry.filterByPlayer', 'Filter by player')}:</span>
                    <Select value={selectedPlayer} onValueChange={setSelectedPlayer}>
                        <SelectTrigger className="w-[160px]"><SelectValue placeholder={t('chemistry.allPlayers', 'All players')} /></SelectTrigger>
                        <SelectContent>
                           {/* <SelectItem value="">All players</SelectItem> */}
                           {players.map((player) => (<SelectItem key={player.id} value={player.id.toString()}>{player.name}</SelectItem>))}
                        </SelectContent>
                    </Select>
                </div>
                <div className="flex items-center gap-6 w-full max-w-xl">
                    <div className="text-sm text-muted-foreground whitespace-nowrap min-w-fit">
                        {t('chemistry.minGames', 'Min. games')}: <span className="font-medium text-foreground">{minGames}</span>
                    </div>
                    <div className="flex-1 px-2">
                        <Slider
                            value={[minGames]}
                            onValueChange={(value) => setMinGames(value[0])}
                            max={maxGamesPlayed}
                            min={0}
                            step={1}
                            className="w-full h-6"
                            aria-label={`Minimum games filter, current value: ${minGames}, maximum: ${maxGamesPlayed}`}
                        />
                    </div>
                </div>
            </div>

            {/* Tabs for View Mode */}
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-full">
              <TabsList className="grid w-full grid-cols-3 md:w-[400px]">
                <TabsTrigger value="duos"><Handshake className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.duos', 'Duos')}</TabsTrigger>
                <TabsTrigger value="trios"><Users className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.trios', 'Trios')}</TabsTrigger>
                <TabsTrigger value="quads"><UsersRound className="h-4 w-4 mr-1 inline-block"/>{t('chemistry.quads', 'Quads')}</TabsTrigger>
              </TabsList>
              {renderTableContent()}
            </Tabs>
          </CardContent>
        </Card>

        {/* Card 2: Scatter Plot */}
        <Card className="lg:col-span-1" noScale>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <BarChartHorizontal className="h-5 w-5 text-indigo-600" />
                    {t('chemistry.scatterPlotTitle', 'Chemistry Scatter Plot')} ({viewMode})
                </CardTitle>
                <CardDescription>
                    {t('chemistry.scatterPlotDescription', 'Visualizing win rate vs. games played for the selected group size.')}
                </CardDescription>
            </CardHeader>
            <CardContent>
                {scatterData.length === 0 ? (
                    <p className="text-center text-muted-foreground py-10">{t('chemistry.noDataForChart', 'No data available for the current filters to display the chart.')}</p>
                ) : (
                    <div className="h-[400px] w-full">
                        <ResponsiveContainer width="100%" height={400}>
                          <ScatterChart margin={{ top: 20, right: 20, bottom: 25, left: 20 }}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              type="number"
                              dataKey="x"
                              name={t('chemistry.gamesPlayed', 'Games Played')}
                              domain={[0, 'auto']}
                              allowDecimals={false}
                            >
                              <RechartsLabel value={t('chemistry.gamesPlayed', 'Games Played')} offset={-20} position="insideBottom" />
                            </XAxis>
                            <YAxis
                              type="number"
                              dataKey="y"
                              name={t('chemistry.winRate', 'Win Rate')}
                              unit="%"
                              domain={[0, 100]}
                            >
                              <RechartsLabel value={t('chemistry.winRatePercent', 'Win Rate (%)')} angle={-90} position="insideLeft" style={{ textAnchor: 'middle' }} />
                            </YAxis>
                            <Scatter
                              name="Chemistry Groups"
                              data={scatterData}
                              fill="#6366f1"
                              shape="circle"
                              isAnimationActive={false}
                            />
                            <Tooltip content={<CustomTooltip />} />
                          </ScatterChart>
                        </ResponsiveContainer>
                    </div>
                 )}
            </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ChemistryPage;
