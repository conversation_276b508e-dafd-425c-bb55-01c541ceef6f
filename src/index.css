@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light mode - using #f4f2ed as the lightest color */
    --background: 43 22% 95%;
    --foreground: 0 0% 6%;

    --card: 43 22% 95%;
    --card-foreground: 0 0% 6%;

    --popover: 43 22% 95%;
    --popover-foreground: 0 0% 6%;

    /* Primary color: #35db71 - Updated to user preference */
    --primary: 145 70% 53%;
    --primary-foreground: 0 0% 100%;

    /* Secondary colors - shades of primary */
    --secondary: 145 70% 45%;
    --secondary-foreground: 0 0% 100%;

    --muted: 43 22% 90%;
    --muted-foreground: 0 0% 40%;

    --accent: 145 70% 93%;
    --accent-foreground: 145 70% 15%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 43 22% 85%;
    --input: 43 22% 85%;
    --ring: 145 70% 33%;

    --radius: 0.5rem;

    --sidebar-background: 43 22% 95%;
    --sidebar-foreground: 0 0% 20%;
    --sidebar-primary: 145 70% 33%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 145 70% 93%;
    --sidebar-accent-foreground: 145 70% 15%;
    --sidebar-border: 43 22% 85%;
    --sidebar-ring: 145 70% 33%;
  }

  .dark {
    /* Dark mode - using #0f0f0f as the darkest color */
    --background: 0 0% 6%;
    --foreground: 43 22% 95%;

    --card: 0 0% 9%;
    --card-foreground: 43 22% 95%;

    --popover: 0 0% 9%;
    --popover-foreground: 43 22% 95%;

    /* Primary color: #35db71 - Updated to user preference */
    --primary: 145 70% 53%;
    --primary-foreground: 0 0% 6%;

    /* Secondary colors - darker shades of primary */
    --secondary: 145 70% 45%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 15%;
    --muted-foreground: 43 22% 80%;

    --accent: 145 70% 15%;
    --accent-foreground: 43 22% 95%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 43 22% 95%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 145 70% 33%;

    --sidebar-background: 0 0% 9%;
    --sidebar-foreground: 43 22% 95%;
    --sidebar-primary: 145 70% 33%;
    --sidebar-primary-foreground: 0 0% 6%;
    --sidebar-accent: 145 70% 15%;
    --sidebar-accent-foreground: 43 22% 95%;
    --sidebar-border: 0 0% 20%;
    --sidebar-ring: 145 70% 33%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Dashboard tabs mobile styling - Enhanced */
@layer components {
  .dashboard-tab {
    @apply transition-all duration-300 relative;
  }

  .dashboard-tab:hover .tab-label {
    @apply md:text-foreground;
  }

  /* Enhanced hover state for dashboard tabs */
  .dashboard-tab:hover {
    @apply bg-soccer-primary/5 text-soccer-primary transition-all duration-300;
  }

  /* Enhanced active state for dashboard tabs - matching main navigation */
  /* Use data attribute selectors to match TabsTrigger specificity */
  .dashboard-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    color: rgb(53 219 113) !important;
    border: 1px solid rgb(53 219 113 / 0.2) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  }

  .dashboard-tab[data-state="active"]:hover {
    background-color: rgb(53 219 113 / 0.15) !important;
    color: rgb(53 219 113) !important;
  }

  /* Dark mode adjustments for dashboard tabs */
  .dark .dashboard-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    color: rgb(53 219 113) !important;
    border: 1px solid rgb(53 219 113 / 0.2) !important;
  }

  .dark .dashboard-tab[data-state="active"]:hover {
    background-color: rgb(53 219 113 / 0.15) !important;
    color: rgb(53 219 113) !important;
  }

  /* Icon animations */
  .dashboard-tab .tab-icon {
    @apply transition-transform duration-300;
  }

  .dashboard-tab:hover .tab-icon {
    @apply scale-110;
  }

  .dashboard-tab[data-state="active"] .tab-icon {
    @apply scale-125;
  }

  /* Enhanced card animations */
  .enhanced-card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-soccer-primary/20 hover:scale-105 hover:border-soccer-primary/40;
  }

  /* Smooth focus states */
  .focus-ring {
    @apply focus-visible:ring-2 focus-visible:ring-soccer-primary focus-visible:ring-offset-2 focus-visible:outline-none;
  }

  /* Gradient text utilities */
  .gradient-text-primary {
    @apply bg-gradient-to-r from-soccer-primary to-soccer-primary-light bg-clip-text text-transparent;
  }

  /* Enhanced button animations */
  .btn-hover-lift {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95;
  }

  /* Smooth page transitions */
  .page-transition {
    @apply transition-all duration-500 ease-in-out;
  }

  /* Team generator tabs without hover animations */
  .team-generator-tab {
    @apply relative;
  }

  .team-generator-tab[data-state="active"] {
    background-color: rgb(53 219 113 / 0.1) !important;
    color: rgb(53 219 113) !important;
    border: 1px solid rgb(53 219 113 / 0.2) !important;
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05) !important;
  }

  /* Dark mode adjustments for team generator tabs */
  .dark .team-generator-tab[data-state="active"] {
    background-color: rgb(255 255 255 / 0.1) !important;
    color: rgb(255 255 255) !important;
    border: 1px solid rgb(255 255 255 / 0.2) !important;
  }

  /* Shimmer animation for skeletons */
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
}