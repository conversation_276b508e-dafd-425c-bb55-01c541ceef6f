import * as React from "react"

import { cn } from "@/lib/utils"

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  interactive?: boolean;
  hoverable?: boolean;
  noScale?: boolean;
  variant?: "default" | "outline" | "secondary" | "primary" | "destructive";
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, interactive = false, hoverable = true, noScale = false, variant = "default", ...props }, ref) => {
    const variantStyles = {
      default: "rounded-lg border bg-card text-card-foreground shadow-sm",
      outline: "rounded-lg border-2 bg-card text-card-foreground shadow-sm",
      secondary: "rounded-lg border bg-secondary text-secondary-foreground shadow-sm",
      primary: "rounded-lg border bg-primary/5 border-primary/20 text-card-foreground shadow-sm",
      destructive: "rounded-lg border bg-destructive/5 border-destructive/20 text-card-foreground shadow-sm",
    };

    return (
      <div
        ref={ref}
        className={cn(
          variantStyles[variant],
          "overflow-hidden transition-all duration-300 ease-in-out",
          hoverable && !noScale && "hover:shadow-lg hover:shadow-soccer-primary/20 hover:border-soccer-primary/40 hover:scale-105 hover:bg-soccer-primary/5 dark:hover:shadow-soccer-primary/30 dark:hover:border-soccer-primary/50 dark:hover:bg-soccer-primary/10",
          hoverable && noScale && "hover:shadow-xl hover:shadow-soccer-primary/25 hover:border-soccer-primary/50 hover:bg-soccer-primary/8 dark:hover:shadow-soccer-primary/35 dark:hover:border-soccer-primary/60 dark:hover:bg-soccer-primary/15",
          interactive && "cursor-pointer transform hover:-translate-y-2 active:translate-y-0 hover:shadow-xl",
          className
        )}
        {...props}
      />
    );
  }
);
Card.displayName = "Card"

const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6 pb-3", className)}
    {...props}
  />
))
CardHeader.displayName = "CardHeader"

const CardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-xl font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
CardTitle.displayName = "CardTitle"

const CardDescription = React.forwardRef<
  HTMLDivElement,  // Changed from HTMLParagraphElement
  React.HTMLAttributes<HTMLDivElement> // Changed from HTMLParagraphElement
>(({ className, ...props }, ref) => (
  <div  // Changed from p
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
CardDescription.displayName = "CardDescription"

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("p-6 pt-0", className)}
    {...props}
  />
))
CardContent.displayName = "CardContent"

const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col p-6 pt-0", className)}
    {...props}
  />
))
CardFooter.displayName = "CardFooter"

// Add compound component pattern
Card.Header = CardHeader;
Card.Title = CardTitle;
Card.Description = CardDescription;
Card.Content = CardContent;
Card.Footer = CardFooter;

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }
