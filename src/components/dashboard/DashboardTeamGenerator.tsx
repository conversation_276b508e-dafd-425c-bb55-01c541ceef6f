import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Zap, Edit } from "lucide-react";
import PlayerSelectionCard from "@/components/team-generator/PlayerSelectionCard";
import TeamsDisplayCard from "@/components/team-generator/TeamsDisplayCard";
import SaveMatchDialog from "@/components/team-generator/SaveMatchDialog";
import { TeamPreference } from "@/components/team-generator/TeamPreferences";
import { SimpleButtonEditor } from "@/components/team-generator/SimpleButtonEditor";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { EnhancedCard } from "@/components/ui/enhanced-card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Types
interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
}

interface Match {
  id: number;
  date: Date;
  teamA: number[];
  teamB: number[];
  scoreA: number | null;
  scoreB: number | null;
  winner?: 'A' | 'B' | 'Draw';
  group_id?: string;
}

type GameFormatKey = "5v5" | "6v6" | "7v7" | "8v8" | "11v11";
type GenerationMode = "balanced" | "random" | "skills" | "effort";

interface GameFormat {
  title: string;
  playersPerTeam: number;
}

const gameFormats: Record<GameFormatKey, GameFormat> = {
  "5v5": { title: "5v5", playersPerTeam: 5 },
  "6v6": { title: "6v6", playersPerTeam: 6 },
  "7v7": { title: "7v7", playersPerTeam: 7 },
  "8v8": { title: "8v8", playersPerTeam: 8 },
  "11v11": { title: "11v11", playersPerTeam: 11 },
};

interface DashboardTeamGeneratorProps {
  players: Player[];
  matches: Match[];
  onSaveMatch?: (match: Partial<Match>) => void;
}

const DashboardTeamGenerator = ({ players, matches, onSaveMatch }: DashboardTeamGeneratorProps) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlayers, setSelectedPlayers] = useState<number[]>([]);
  const [teamA, setTeamA] = useState<Player[]>([]);
  const [teamB, setTeamB] = useState<Player[]>([]);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);
  const [scoreA, setScoreA] = useState<number>(0);
  const [scoreB, setScoreB] = useState<number>(0);
  const [format, setFormat] = useState<GameFormatKey>("5v5");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("balanced");
  const [activeTab, setActiveTab] = useState<string>("auto");

  // Filter available players based on search query with comma support
  const filteredAvailablePlayers = players.filter(player => {
    if (!searchQuery.trim()) return true;

    // Split the search query by commas and trim each term
    const searchTerms = searchQuery.split(',').map(term => term.trim().toLowerCase());

    // Check if any of the search terms match the player name
    return searchTerms.some(term =>
      term && player.name.toLowerCase().includes(term)
    );
  });

  // Check if teams have been generated
  const hasGeneratedTeams = teamA.length > 0 || teamB.length > 0;

  // Handle player selection toggle
  const handlePlayerToggle = (playerId: number) => {
    setSelectedPlayers(prev => {
      if (prev.includes(playerId)) {
        return prev.filter(id => id !== playerId);
      } else {
        return [...prev, playerId];
      }
    });
  };

  // Handle select all players
  const handleSelectAll = () => {
    if (selectedPlayers.length === filteredAvailablePlayers.length) {
      setSelectedPlayers([]);
    } else {
      setSelectedPlayers(filteredAvailablePlayers.map(p => p.id));
    }
  };

  // Generate teams utility function
  const generateTeamsUtil = (
    selectedPlayerIds: number[],
    allPlayers: Player[],
    format: GameFormatKey,
    mode: GenerationMode
  ) => {
    const selectedPlayerObjects = allPlayers.filter(p => selectedPlayerIds.includes(p.id));

    // Sort players based on generation mode
    let sortedPlayers = [...selectedPlayerObjects];
    if (mode === "skills") {
      sortedPlayers.sort((a, b) => b.skills - a.skills);
    } else if (mode === "effort") {
      sortedPlayers.sort((a, b) => b.effort - a.effort);
    } else if (mode === "balanced") {
      sortedPlayers.sort((a, b) => {
        const avgA = (a.skills + a.effort + a.stamina) / 3;
        const avgB = (b.skills + b.effort + b.stamina) / 3;
        return avgB - avgA;
      });
    } else {
      // Random mode - shuffle array
      for (let i = sortedPlayers.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [sortedPlayers[i], sortedPlayers[j]] = [sortedPlayers[j], sortedPlayers[i]];
      }
    }

    // Distribute players to teams
    const teamA: Player[] = [];
    const teamB: Player[] = [];

    if (mode === "balanced") {
      // Balanced distribution (alternating)
      sortedPlayers.forEach((player, index) => {
        if (index % 2 === 0) {
          teamA.push(player);
        } else {
          teamB.push(player);
        }
      });
    } else {
      // For other modes, split the sorted list in half
      const halfIndex = Math.ceil(sortedPlayers.length / 2);
      teamA.push(...sortedPlayers.slice(0, halfIndex));
      teamB.push(...sortedPlayers.slice(halfIndex));
    }

    return { teamA, teamB };
  };

  // Generate teams based on selected players
  const generateTeams = () => {
    const playersNeeded = gameFormats[format].playersPerTeam * 2;

    if (selectedPlayers.length < playersNeeded) {
      toast({
        title: t('teamGenerator.notEnoughPlayers', 'Not Enough Players'),
        description: t('teamGenerator.selectAtLeast', 'Please select at least {{count}} players for the {{format}} format. You have {{selected}}.', { count: playersNeeded, format: gameFormats[format].title, selected: selectedPlayers.length }),
        variant: "destructive"
      });
      return;
    }

    if (selectedPlayers.length > playersNeeded) {
      toast({
        title: t('teamGenerator.tooManyPlayers', 'Too Many Players'),
        description: t('teamGenerator.selectExactly', 'Please select exactly {{count}} players for the {{format}} format. You have {{selected}}.', { count: playersNeeded, format: gameFormats[format].title, selected: selectedPlayers.length }),
        variant: "destructive"
      });
      return;
    }

    const { teamA: newTeamA, teamB: newTeamB } = generateTeamsUtil(
      selectedPlayers,
      players,
      format,
      generationMode
    );

    setTeamA(newTeamA);
    setTeamB(newTeamB);
  };

  // Handle quick generate with preset preferences
  const handleQuickGenerate = (preference: TeamPreference) => {
    setFormat(preference.format);
    setGenerationMode(preference.generationMode);

    // Auto-select the right number of players if possible
    const playersNeeded = gameFormats[preference.format].playersPerTeam * 2;
    if (players.length >= playersNeeded) {
      setSelectedPlayers(players.slice(0, playersNeeded).map(p => p.id));
    }

    // Generate teams with the new settings
    setTimeout(() => {
      const { teamA: newTeamA, teamB: newTeamB } = generateTeamsUtil(
        selectedPlayers,
        players,
        preference.format,
        preference.generationMode
      );
      setTeamA(newTeamA);
      setTeamB(newTeamB);
    }, 0);
  };

  // Handle save match
  const handleSaveMatch = (winner: 'A' | 'B' | 'Draw') => {
    const newMatch: Partial<Match> = {
      date: new Date(),
      teamA: teamA.map(p => p.id),
      teamB: teamB.map(p => p.id),
      scoreA,
      scoreB,
      winner
    };

    if (onSaveMatch) {
      onSaveMatch(newMatch);
    }

    setIsSaveModalOpen(false);
    setTeamA([]);
    setTeamB([]);
    setSelectedPlayers([]);
    setScoreA(0);
    setScoreB(0);

    toast({
      title: t('matches.matchSaved', 'Match Saved'),
      description: t('matches.matchSavedDescription', 'The match has been saved successfully.'),
    });
  };

  return (
    <div className="space-y-6">
      {/* Team Generator Header Card */}
      <EnhancedCard hoverable noScale>
        <div className="p-6">
          <div className="flex items-center gap-3 mb-4">
            <Zap className="h-5 w-5 text-soccer-primary" />
            <div className="font-semibold text-lg text-white">{t('nav.teamGenerator')}</div>
          </div>
          <div className="text-sm text-muted-foreground">
            {t('teamGenerator.description', 'Generate balanced teams for your matches')}
          </div>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full mt-4">
            <TabsList className="grid w-full grid-cols-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="auto" className="team-generator-tab">
                      <Zap className="h-4 w-4 md:mr-2" />
                      <span className="hidden md:inline tab-label">{t('teamGenerator.automatic')}</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent className="md:hidden">
                    {t('teamGenerator.automatic')}
                  </TooltipContent>
                </Tooltip>

                <Tooltip>
                  <TooltipTrigger asChild>
                    <TabsTrigger value="manual" className="team-generator-tab">
                      <Edit className="h-4 w-4 md:mr-2" />
                      <span className="hidden md:inline tab-label">{t('teamGenerator.manual')}</span>
                    </TabsTrigger>
                  </TooltipTrigger>
                  <TooltipContent className="md:hidden">
                    {t('teamGenerator.manual')}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TabsList>

          <TabsContent value="auto" className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div>
                <PlayerSelectionCard
                  players={filteredAvailablePlayers}
                  selectedPlayers={selectedPlayers}
                  format={format}
                  gameFormats={gameFormats}
                  generationMode={generationMode}
                  searchQuery={searchQuery}
                  onSearchChange={setSearchQuery}
                  onPlayerToggle={handlePlayerToggle}
                  onSelectAll={handleSelectAll}
                  onFormatChange={(value) => setFormat(value as GameFormatKey)}
                  onGenerationModeChange={setGenerationMode}
                  onGenerateTeams={generateTeams}
                  onQuickGenerate={handleQuickGenerate}
                />
              </div>

              <div>
                <TeamsDisplayCard
                  teamA={teamA}
                  teamB={teamB}
                  hasGeneratedTeams={hasGeneratedTeams}
                  onSaveMatch={() => {
                    if (!hasGeneratedTeams) {
                      toast({
                        title: t('teamGenerator.noTeamsGenerated', 'No Teams Generated'),
                        description: t('teamGenerator.generateTeamsFirst', 'Generate teams first before saving.'),
                        variant: "destructive"
                      });
                      return;
                    }
                    setIsSaveModalOpen(true);
                  }}
                />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="manual">
            <SimpleButtonEditor
              teamA={teamA}
              teamB={teamB}
              availablePlayers={players}
              onTeamsChange={(newTeamA, newTeamB) => {
                setTeamA(newTeamA);
                setTeamB(newTeamB);
              }}
              onSaveTeams={() => {
                setIsSaveModalOpen(true);
              }}
            />
          </TabsContent>
        </Tabs>
        </div>
      </EnhancedCard>

      <SaveMatchDialog
        open={isSaveModalOpen}
        onOpenChange={setIsSaveModalOpen}
        teamA={teamA}
        teamB={teamB}
        scoreA={scoreA}
        scoreB={scoreB}
        onScoreAChange={setScoreA}
        onScoreBChange={setScoreB}
        onSave={handleSaveMatch}
      />
    </div>
  );
};

export default DashboardTeamGenerator;
