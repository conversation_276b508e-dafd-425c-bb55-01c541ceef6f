
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Shuffle, Scale, Zap, Search, Bookmark } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Player, GameFormat, GenerationMode } from "@/utils/teamGenerator";
import { useTranslation } from "react-i18next";
import { TeamPreferences, TeamPreference } from "./TeamPreferences";

interface PlayerSelectionCardProps {
  players: Player[];
  selectedPlayers: number[];
  format: string;
  gameFormats: Record<string, GameFormat>;
  generationMode: GenerationMode;
  searchQuery?: string;
  onSearchChange?: (query: string) => void;
  onPlayerToggle: (playerId: number) => void;
  onSelectAll: () => void;
  onFormatChange: (value: string) => void;
  onGenerationModeChange: (mode: GenerationMode) => void;
  onGenerateTeams: () => void;
  onQuickGenerate?: (preference: TeamPreference) => void;
}

const PlayerSelectionCard = ({
  players,
  selectedPlayers,
  format,
  gameFormats,
  generationMode,
  searchQuery = "",
  onSearchChange = () => {},
  onPlayerToggle,
  onSelectAll,
  onFormatChange,
  onGenerationModeChange,
  onGenerateTeams,
  onQuickGenerate,
}: PlayerSelectionCardProps) => {
  const { t } = useTranslation();
  const allSelected = selectedPlayers.length === players.length;
  const playersNeeded = gameFormats[format].playersPerTeam * 2;

  return (
    <Card hoverable={false}>
      <CardHeader>
        <CardTitle>{t('teamGenerator.availablePlayers')}</CardTitle>
        <CardDescription>{t('teamGenerator.selectPlayersAndOptions', 'Select players and generation options')}</CardDescription>
        {/* Add search input */}
        <div className="relative mt-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={t('teamGenerator.searchPlayersWithCommas', 'Search players (separate multiple names with commas)...')}
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-8"
          />
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs defaultValue="players">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="players">{t('common.players', 'Players')}</TabsTrigger>
            <TabsTrigger value="preferences">{t('teamGenerator.savedPreferences', 'Saved Preferences')}</TabsTrigger>
          </TabsList>

          <TabsContent value="players" className="space-y-4 pt-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="selectAll"
                  checked={allSelected}
                  onCheckedChange={onSelectAll}
                />
                <Label htmlFor="selectAll" className="font-medium">
                  {t('teamGenerator.selectAllPlayers', 'Select All Players')}
                </Label>
              </div>
              <div>
                <Select
                  value={format}
                  onValueChange={onFormatChange}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="Format" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(gameFormats).map((fmt) => (
                      <SelectItem key={fmt} value={fmt}>
                        {gameFormats[fmt].title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>{t('teamGenerator.selected', 'Selected')}: {selectedPlayers.length} {t('common.players', 'players')}</span>
              <span>{t('teamGenerator.needed', 'Needed')}: {playersNeeded} {t('common.players', 'players')}</span>
            </div>

            {/* Generation Mode Selector */}
            <div className="pt-2">
                <Label className="text-sm font-medium">{t('teamGenerator.generationMode', 'Generation Mode')}</Label>
                <RadioGroup
                    value={generationMode}
                    onValueChange={(value) => onGenerationModeChange(value as GenerationMode)}
                    className="flex gap-4 mt-2"
                >
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="balanced" id="mode-balanced" />
                        <Label htmlFor="mode-balanced" className="flex items-center gap-1 cursor-pointer">
                            <Scale className="h-4 w-4 text-blue-600"/> {t('teamGenerator.balanceTeams', 'Balanced')}
                        </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                        <RadioGroupItem value="random" id="mode-random" />
                        <Label htmlFor="mode-random" className="flex items-center gap-1 cursor-pointer">
                             <Zap className="h-4 w-4 text-orange-500"/> {t('teamGenerator.random', 'Random')}
                        </Label>
                    </div>
                </RadioGroup>
            </div>

            <Separator />
            <div className="overflow-y-auto pr-2" style={{ height: '180px' }}>
              <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                {[...players]
                  .sort((a, b) => (b.played || 0) - (a.played || 0)) // Sort by games played
                  .map((player) => (
                    <div key={player.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`player-${player.id}`}
                        checked={selectedPlayers.includes(player.id)}
                        onCheckedChange={() => onPlayerToggle(player.id)}
                      />
                      <Label htmlFor={`player-${player.id}`} className="flex justify-between w-full text-sm">
                        <span className="truncate">{player.name}</span>
                        <div className="flex items-center space-x-2 flex-shrink-0">
                          <span className="text-xs text-gray-500">{t('matches.games', 'games')}:</span>
                          <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 px-2 py-0.5 rounded-full">
                            {player.played || 0}
                          </span>
                        </div>
                      </Label>
                    </div>
                  ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preferences" className="pt-4">
            <TeamPreferences
              onQuickGenerate={(preference) => {
                if (onQuickGenerate) {
                  onQuickGenerate(preference);
                }
              }}
              format={format as GameFormatKey}
              generationMode={generationMode}
              selectedPlayers={selectedPlayers}
              gameFormats={gameFormats}
            />
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter>
        <Button
          onClick={onGenerateTeams}
          className="w-full bg-soccer-primary hover:bg-soccer-primary hover:scale-100 hover:shadow-none transition-none"
          disabled={selectedPlayers.length < playersNeeded}
        >
          <Shuffle className="mr-2 h-4 w-4" />
          {t('teamGenerator.generateTeams')} ({generationMode === 'balanced' ? t('teamGenerator.balanceTeams', 'Balanced') : t('teamGenerator.random', 'Random')})
        </Button>
      </CardFooter>
    </Card>
  );
};

export default PlayerSelectionCard;
