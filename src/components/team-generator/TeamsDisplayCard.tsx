
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Save, Shuffle } from "lucide-react";
import { Player } from "@/utils/teamGenerator";
import { useTranslation } from "react-i18next";
import { TeamBalanceChart } from "./TeamBalanceChart";
import { Separator } from "@/components/ui/separator";

interface TeamsDisplayCardProps {
  teamA: Player[];
  teamB: Player[];
  hasGeneratedTeams: boolean;
  onSaveMatch: () => void;
  viewOnly?: boolean;
}

const TeamsDisplayCard = ({
  teamA,
  teamB,
  hasGeneratedTeams,
  onSaveMatch,
  viewOnly = false,
}: TeamsDisplayCardProps) => {
  const { t } = useTranslation();
  const calculateTeamRating = (team: Player[]) => {
    if (!team.length) return 0;
    const totalRating = team.reduce((sum, player) => {
      const playerRating = player.rating ||
        Math.round(((player.skills || 75) + (player.effort || 75) + (player.stamina || 75)) / 3);
      return sum + playerRating;
    }, 0);
    return Math.round(totalRating / team.length);
  };

  return (
    <Card hoverable={false}>
      <CardHeader>
        <CardTitle>{t('teamGenerator.generatedTeams', 'Generated Teams')}</CardTitle>
        <CardDescription>
          {hasGeneratedTeams
            ? t('teamGenerator.teamsBalanced', 'Teams balanced by player ratings')
            : t('teamGenerator.selectPlayersAndGenerate', 'Select players and click Generate Teams to create balanced teams')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {!hasGeneratedTeams ? (
          <div className="flex items-center justify-center h-40 border-2 border-dashed rounded-md">
            <div className="text-center text-muted-foreground">
              <Shuffle className="mx-auto h-8 w-8 mb-2" />
              <p>{t('teamGenerator.noTeamsGenerated', 'No teams generated yet')}</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Team Balance Chart */}
            <TeamBalanceChart teamA={teamA} teamB={teamB} />

            <Separator />

            {/* Team Rosters */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium flex items-center justify-between mb-2">
                  {t('teamGenerator.teamA')}
                  <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 px-2 py-0.5 rounded-full">
                    {t('teamGenerator.avg', 'Avg')}: {calculateTeamRating(teamA)}
                  </span>
                </h3>
                <div className="border rounded-md p-3 bg-gray-50 dark:bg-gray-800 min-h-[200px]">
                  {teamA.map((player) => (
                    <div key={player.id} className="flex justify-between py-1 border-b dark:border-gray-700 last:border-0">
                      <span>{player.name}</span>
                      <span className="text-xs bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 px-2 py-0.5 rounded-full">
                        {player.rating || Math.round(((player.skills || 75) + (player.effort || 75) + (player.stamina || 75)) / 3)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium flex items-center justify-between mb-2">
                  {t('teamGenerator.teamB')}
                  <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-2 py-0.5 rounded-full">
                    {t('teamGenerator.avg', 'Avg')}: {calculateTeamRating(teamB)}
                  </span>
                </h3>
                <div className="border rounded-md p-3 bg-gray-50 dark:bg-gray-800 min-h-[200px]">
                  {teamB.map((player) => (
                    <div key={player.id} className="flex justify-between py-1 border-b dark:border-gray-700 last:border-0">
                      <span>{player.name}</span>
                      <span className="text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-2 py-0.5 rounded-full">
                        {player.rating || Math.round(((player.skills || 75) + (player.effort || 75) + (player.stamina || 75)) / 3)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
      {hasGeneratedTeams && (
        <CardFooter>
          <Button
            onClick={onSaveMatch}
            className="w-full bg-soccer-secondary hover:bg-soccer-secondary hover:scale-100 hover:shadow-none transition-none"
            disabled={viewOnly}
          >
            <Save className="mr-2 h-4 w-4" />
            {viewOnly ? t('teamGenerator.saveAsMatchDisabled', 'Save as Match (Disabled in View Mode)') : t('teamGenerator.saveMatch')}
          </Button>
        </CardFooter>
      )}
    </Card>
  );
};

export default TeamsDisplayCard;
